<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Percentage Calculation Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-result.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-result.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-result.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Percentage Calculation Verification</h1>
        
        <div class="test-section">
            <h2>Actual Dashboard Data Analysis</h2>
            <div id="dashboard-data-analysis"></div>
        </div>

        <div class="test-section">
            <h2>Expected vs Actual Calculations</h2>
            <div id="calculation-comparison"></div>
        </div>

        <div class="test-section">
            <h2>Manual Verification</h2>
            <div id="manual-verification"></div>
        </div>
    </div>

    <script>
        // Import the calculation function from dashboard.js
        function calculateComparisonPercentage(currentValue, previousValue) {
            // Handle edge cases
            if (previousValue === 0) {
                return {
                    percentage: null,
                    isPositive: false,
                    isValid: false,
                    message: 'No previous data available'
                };
            }

            if (currentValue === null || previousValue === null) {
                return {
                    percentage: null,
                    isPositive: false,
                    isValid: false,
                    message: 'Missing data'
                };
            }

            // Calculate percentage change
            const percentageChange = ((currentValue - previousValue) / previousValue) * 100;
            const roundedPercentage = Math.round(percentageChange * 10) / 10; // Round to 1 decimal place

            return {
                percentage: roundedPercentage,
                isPositive: roundedPercentage >= 0,
                isValid: true,
                message: null
            };
        }

        // Actual data from the dashboard (from generateFourSalesCardsMockData function)
        const dashboardData = {
            currentMonth: {
                current: 2847,
                previous: 4523,
                description: 'Current Month vs Last Month'
            },
            lastMonth: {
                current: 4523,
                previous: 3987,
                description: 'Last Month vs Month Before Last'
            },
            currentYear: {
                current: 28947,
                previous: 45234,
                description: 'Current Year vs Last Year'
            },
            lastYear: {
                current: 45234,
                previous: 38765,
                description: 'Last Year vs Year Before Last'
            }
        };

        function runDashboardDataAnalysis() {
            const resultsDiv = document.getElementById('dashboard-data-analysis');
            resultsDiv.innerHTML = '';

            // Create data table
            let tableHTML = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Period</th>
                            <th>Current Value</th>
                            <th>Previous Value</th>
                            <th>Difference</th>
                            <th>Percentage Change</th>
                            <th>Expected Direction</th>
                            <th>Actual Result</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            Object.keys(dashboardData).forEach(period => {
                const data = dashboardData[period];
                const result = calculateComparisonPercentage(data.current, data.previous);
                const difference = data.current - data.previous;
                const expectedDirection = difference >= 0 ? 'Increase' : 'Decrease';
                const actualDirection = result.isPositive ? 'Increase' : 'Decrease';

                tableHTML += `
                    <tr>
                        <td><strong>${period}</strong></td>
                        <td>${data.current.toLocaleString()}</td>
                        <td>${data.previous.toLocaleString()}</td>
                        <td>${difference.toLocaleString()}</td>
                        <td>${result.percentage}%</td>
                        <td>${expectedDirection}</td>
                        <td>${actualDirection}</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            resultsDiv.innerHTML = tableHTML;

            // Add summary
            const summaryDiv = document.createElement('div');
            summaryDiv.className = 'test-result info';
            summaryDiv.innerHTML = `
                <h3>Analysis Summary:</h3>
                <ul>
                    <li><strong>Current Month:</strong> Should show DECREASE (2847 vs 4523)</li>
                    <li><strong>Last Month:</strong> Should show INCREASE (4523 vs 3987)</li>
                    <li><strong>Current Year:</strong> Should show DECREASE (28947 vs 45234)</li>
                    <li><strong>Last Year:</strong> Should show INCREASE (45234 vs 38765)</li>
                </ul>
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        function runCalculationComparison() {
            const resultsDiv = document.getElementById('calculation-comparison');
            resultsDiv.innerHTML = '';

            Object.keys(dashboardData).forEach(period => {
                const data = dashboardData[period];
                const result = calculateComparisonPercentage(data.current, data.previous);
                
                const resultDiv = document.createElement('div');
                resultDiv.className = `test-result ${result.isValid ? 'success' : 'error'}`;
                
                const expectedDirection = (data.current - data.previous) >= 0 ? 'Increase' : 'Decrease';
                const isCorrect = (result.isPositive && expectedDirection === 'Increase') || 
                                (!result.isPositive && expectedDirection === 'Decrease');
                
                resultDiv.innerHTML = `
                    <h3>${period} (${data.description})</h3>
                    <p><strong>Data:</strong> ${data.current.toLocaleString()} vs ${data.previous.toLocaleString()}</p>
                    <p><strong>Calculation:</strong> (${data.current} - ${data.previous}) / ${data.previous} × 100 = ${result.percentage}%</p>
                    <p><strong>Expected:</strong> ${expectedDirection}</p>
                    <p><strong>Actual:</strong> ${result.isPositive ? 'Increase' : 'Decrease'}</p>
                    <p><strong>Correct:</strong> ${isCorrect ? '✅ Yes' : '❌ No'}</p>
                `;
                
                resultsDiv.appendChild(resultDiv);
            });
        }

        function runManualVerification() {
            const resultsDiv = document.getElementById('manual-verification');
            resultsDiv.innerHTML = '';

            const manualTests = [
                {
                    name: 'Current Month Test',
                    current: 2847,
                    previous: 4523,
                    expected: -37.1, // (2847 - 4523) / 4523 * 100 = -37.1%
                    description: 'Should show 37.1% decrease'
                },
                {
                    name: 'Last Month Test',
                    current: 4523,
                    previous: 3987,
                    expected: 13.4, // (4523 - 3987) / 3987 * 100 = 13.4%
                    description: 'Should show 13.4% increase'
                },
                {
                    name: 'Current Year Test',
                    current: 28947,
                    previous: 45234,
                    expected: -36.0, // (28947 - 45234) / 45234 * 100 = -36.0%
                    description: 'Should show 36.0% decrease'
                },
                {
                    name: 'Last Year Test',
                    current: 45234,
                    previous: 38765,
                    expected: 16.7, // (45234 - 38765) / 38765 * 100 = 16.7%
                    description: 'Should show 16.7% increase'
                }
            ];

            manualTests.forEach(test => {
                const result = calculateComparisonPercentage(test.current, test.previous);
                const isCorrect = Math.abs(result.percentage - test.expected) < 0.1; // Allow for rounding differences
                
                const resultDiv = document.createElement('div');
                resultDiv.className = `test-result ${isCorrect ? 'success' : 'error'}`;
                resultDiv.innerHTML = `
                    <h3>${test.name}</h3>
                    <p><strong>Calculation:</strong> ${test.description}</p>
                    <p><strong>Expected:</strong> ${test.expected}%</p>
                    <p><strong>Actual:</strong> ${result.percentage}%</p>
                    <p><strong>Correct:</strong> ${isCorrect ? '✅ Yes' : '❌ No'}</p>
                `;
                
                resultsDiv.appendChild(resultDiv);
            });
        }

        // Run all tests when page loads
        window.addEventListener('load', function() {
            runDashboardDataAnalysis();
            runCalculationComparison();
            runManualVerification();
        });
    </script>
</body>
</html> 